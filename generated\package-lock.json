{"name": "Nlymo", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "Nlymo", "version": "1.0.0", "dependencies": {"axios": "^1.5.0", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.2", "helmet": "^7.0.0", "http-proxy-middleware": "^2.0.6", "express": "^4.18.2", "ioredis": "^5.3.2", "body-parser": "^1.20.2", "pg": "^8.11.3", "dotenv": "^16.3.1", "geolib": "^3.3.4"}, "devDependencies": {"nodemon": "^2.0.0"}, "engines": {"node": ">=14.0.0"}}}, "dependencies": {"axios": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.5.0.tgz", "integrity": "sha512-placeholder"}, "cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-placeholder"}, "jsonwebtoken": {"version": "9.0.2", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha512-placeholder"}, "socket.io": {"version": "4.7.2", "resolved": "https://registry.npmjs.org/socket.io/-/socket.io-4.7.2.tgz", "integrity": "sha512-placeholder"}, "helmet": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/helmet/-/helmet-7.0.0.tgz", "integrity": "sha512-placeholder"}, "http-proxy-middleware": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz", "integrity": "sha512-placeholder"}, "express": {"version": "4.18.2", "resolved": "https://registry.npmjs.org/express/-/express-4.18.2.tgz", "integrity": "sha512-placeholder"}, "ioredis": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/ioredis/-/ioredis-5.3.2.tgz", "integrity": "sha512-placeholder"}, "body-parser": {"version": "1.20.2", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "integrity": "sha512-placeholder"}, "pg": {"version": "8.11.3", "resolved": "https://registry.npmjs.org/pg/-/pg-8.11.3.tgz", "integrity": "sha512-placeholder"}, "dotenv": {"version": "16.3.1", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.3.1.tgz", "integrity": "sha512-placeholder"}, "geolib": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/geolib/-/geolib-3.3.4.tgz", "integrity": "sha512-placeholder"}}}