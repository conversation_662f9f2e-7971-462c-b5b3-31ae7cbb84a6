{"name": "Nlymo", "version": "1.0.0", "description": null, "main": "server.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.0.0", "cors": "^2.8.0", "jsonwebtoken": "^1.0.0", "socket.io": "^1.0.0", "helmet": "^5.0.0", "http-proxy-middleware": "^1.0.0", "express": "^4.18.0", "ioredis": "^1.0.0", "body-parser": "^1.0.0", "pg": "^1.0.0", "dotenv": "^16.0.0", "geolib": "^1.0.0", "os": "^1.0.0"}, "devDependencies": {"nodemon": "^2.0.0"}, "engines": {"node": ">=14.0.0"}}