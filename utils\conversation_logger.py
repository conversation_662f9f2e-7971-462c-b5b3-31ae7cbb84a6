"""
Comprehensive logging system for Repo Runner conversations, prompts, and LLM interactions.
"""
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path


class ConversationLogger:
    """Logger for tracking all LLM interactions and flow execution."""
    
    def __init__(self, log_dir: str = "logs", session_id: Optional[str] = None):
        """
        Initialize the conversation logger.
        
        Args:
            log_dir: Directory to store log files
            session_id: Unique session identifier (auto-generated if None)
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Generate session ID if not provided
        if session_id is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = f"repo_runner_{timestamp}"
        
        self.session_id = session_id
        self.session_start = datetime.now()
        
        # Create session-specific log files
        self.conversation_log = self.log_dir / f"{session_id}_conversation.jsonl"
        self.flow_log = self.log_dir / f"{session_id}_flow.jsonl"
        self.summary_log = self.log_dir / f"{session_id}_summary.json"
        
        # Initialize session data
        self.session_data = {
            "session_id": session_id,
            "start_time": self.session_start.isoformat(),
            "repository_url": None,
            "total_llm_calls": 0,
            "total_tokens_used": 0,
            "nodes_executed": [],
            "flow_transitions": [],
            "errors": [],
            "warnings": []
        }
        
        # Setup file logging
        self._setup_file_logging()
        
        self.log_session_start()
    
    def _setup_file_logging(self):
        """Setup file-based logging handlers."""
        # Create detailed log file for all events
        detailed_log = self.log_dir / f"{self.session_id}_detailed.log"

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Create file handler
        self.file_handler = logging.FileHandler(detailed_log, encoding='utf-8')
        self.file_handler.setLevel(logging.DEBUG)
        self.file_handler.setFormatter(formatter)

        # Add handler to root logger
        logger = logging.getLogger()
        logger.addHandler(self.file_handler)
        logger.setLevel(logging.DEBUG)  # Ensure root logger level allows all messages

        # Log initial setup
        logging.info(f"Conversation logger initialized for session: {self.session_id}")
        logging.info(f"Detailed log file: {detailed_log}")
    
    def log_session_start(self):
        """Log the start of a new session."""
        self._write_to_flow_log({
            "event": "session_start",
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id
        })
    
    def log_repository_info(self, repository_url: str, access_token: bool = False):
        """Log repository information."""
        self.session_data["repository_url"] = repository_url
        
        self._write_to_flow_log({
            "event": "repository_initialized",
            "timestamp": datetime.now().isoformat(),
            "repository_url": repository_url,
            "has_access_token": access_token
        })
    
    def log_llm_interaction(self, 
                           node_name: str,
                           prompt: str, 
                           response: str,
                           model: str = "gemini-2.5-flash",
                           temperature: float = 0.1,
                           tokens_used: int = 0,
                           duration: float = 0.0,
                           success: bool = True,
                           error: Optional[str] = None):
        """
        Log a complete LLM interaction.
        
        Args:
            node_name: Name of the node making the LLM call
            prompt: The prompt sent to the LLM
            response: The response received from the LLM
            model: LLM model used
            temperature: Temperature setting
            tokens_used: Number of tokens consumed
            duration: Time taken for the call
            success: Whether the call was successful
            error: Error message if call failed
        """
        self.session_data["total_llm_calls"] += 1
        self.session_data["total_tokens_used"] += tokens_used
        
        interaction_data = {
            "event": "llm_interaction",
            "timestamp": datetime.now().isoformat(),
            "node_name": node_name,
            "model": model,
            "temperature": temperature,
            "tokens_used": tokens_used,
            "duration_seconds": duration,
            "success": success,
            "prompt_length": len(prompt),
            "response_length": len(response) if response else 0,
            "prompt": prompt,
            "response": response,
            "error": error
        }
        
        self._write_to_conversation_log(interaction_data)
        
        if error:
            self.session_data["errors"].append({
                "timestamp": datetime.now().isoformat(),
                "node": node_name,
                "error": error
            })
    
    def log_node_execution(self, 
                          node_name: str,
                          node_type: str,
                          input_data: Dict[str, Any],
                          output_data: Dict[str, Any],
                          duration: float,
                          success: bool = True,
                          error: Optional[str] = None):
        """Log node execution details."""
        self.session_data["nodes_executed"].append({
            "node_name": node_name,
            "timestamp": datetime.now().isoformat(),
            "success": success
        })
        
        execution_data = {
            "event": "node_execution",
            "timestamp": datetime.now().isoformat(),
            "node_name": node_name,
            "node_type": node_type,
            "duration_seconds": duration,
            "success": success,
            "input_keys": list(input_data.keys()) if input_data else [],
            "output_keys": list(output_data.keys()) if output_data else [],
            "input_data": self._sanitize_data(input_data),
            "output_data": self._sanitize_data(output_data),
            "error": error
        }
        
        self._write_to_flow_log(execution_data)
        
        if error:
            self.session_data["errors"].append({
                "timestamp": datetime.now().isoformat(),
                "node": node_name,
                "error": error
            })
    
    def log_flow_transition(self, 
                           from_node: str,
                           to_node: str,
                           action: str,
                           shared_state_keys: List[str]):
        """Log flow transitions between nodes."""
        self.session_data["flow_transitions"].append({
            "from": from_node,
            "to": to_node,
            "action": action,
            "timestamp": datetime.now().isoformat()
        })
        
        transition_data = {
            "event": "flow_transition",
            "timestamp": datetime.now().isoformat(),
            "from_node": from_node,
            "to_node": to_node,
            "action": action,
            "shared_state_keys": shared_state_keys
        }
        
        self._write_to_flow_log(transition_data)
    
    def log_warning(self, message: str, node_name: Optional[str] = None):
        """Log a warning message."""
        self.session_data["warnings"].append({
            "timestamp": datetime.now().isoformat(),
            "node": node_name,
            "message": message
        })
        
        logging.warning(f"[{node_name}] {message}" if node_name else message)
    
    def log_error(self, message: str, node_name: Optional[str] = None, exception: Optional[Exception] = None):
        """Log an error message."""
        error_data = {
            "timestamp": datetime.now().isoformat(),
            "node": node_name,
            "message": message,
            "exception": str(exception) if exception else None
        }
        
        self.session_data["errors"].append(error_data)
        logging.error(f"[{node_name}] {message}" if node_name else message)
    
    def finalize_session(self, results: Dict[str, Any]):
        """Finalize the session and write summary."""
        self.session_data["end_time"] = datetime.now().isoformat()
        self.session_data["duration_seconds"] = (datetime.now() - self.session_start).total_seconds()
        self.session_data["final_results"] = self._sanitize_data(results)
        
        # Write final summary
        with open(self.summary_log, 'w', encoding='utf-8') as f:
            json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        
        self._write_to_flow_log({
            "event": "session_end",
            "timestamp": datetime.now().isoformat(),
            "total_duration": self.session_data["duration_seconds"],
            "total_llm_calls": self.session_data["total_llm_calls"],
            "total_nodes_executed": len(self.session_data["nodes_executed"]),
            "success": len(self.session_data["errors"]) == 0
        })
    
    def _write_to_conversation_log(self, data: Dict[str, Any]):
        """Write data to conversation log file."""
        with open(self.conversation_log, 'a', encoding='utf-8') as f:
            f.write(json.dumps(data, ensure_ascii=False) + '\n')
    
    def _write_to_flow_log(self, data: Dict[str, Any]):
        """Write data to flow log file."""
        with open(self.flow_log, 'a', encoding='utf-8') as f:
            f.write(json.dumps(data, ensure_ascii=False) + '\n')
    
    def _sanitize_data(self, data: Any, max_length: int = 1000) -> Any:
        """Sanitize data for logging (truncate long strings, remove sensitive info)."""
        if isinstance(data, dict):
            return {k: self._sanitize_data(v, max_length) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_data(item, max_length) for item in data]
        elif isinstance(data, str):
            if len(data) > max_length:
                return data[:max_length] + "... [TRUNCATED]"
            return data
        else:
            return data


# Global logger instance
_conversation_logger: Optional[ConversationLogger] = None


def get_conversation_logger() -> ConversationLogger:
    """Get the global conversation logger instance."""
    global _conversation_logger
    if _conversation_logger is None:
        _conversation_logger = ConversationLogger()
    return _conversation_logger


def initialize_conversation_logger(session_id: Optional[str] = None) -> ConversationLogger:
    """Initialize a new conversation logger session."""
    global _conversation_logger
    _conversation_logger = ConversationLogger(session_id=session_id)
    return _conversation_logger
