"""
Gap detector for identifying missing components and issues in repositories.
"""
import os
from typing import Dict, List, Set, Any
import re


def detect_gaps(repository_analysis: Dict[str, Any], file_tree: List[Dict]) -> Dict[str, Any]:
    """
    Detect gaps and missing components in the repository.
    
    Args:
        repository_analysis: Current knowledge about the repository
        file_tree: List of files and directories in the repository
    
    Returns:
        Dictionary containing identified gaps and issues
    """
    gaps = {
        'missing_files': [],
        'missing_dependencies': [],
        'configuration_issues': [],
        'code_issues': [],
        'security_issues': [],
        'containerization_gaps': [],
        'critical_gaps': [],
        'severity': 'low'
    }
    
    # Get file names for quick lookup
    file_names = {item['name'].lower() for item in file_tree if item['type'] == 'file'}
    file_paths = {item['path'].lower() for item in file_tree if item['type'] == 'file'}
    
    # Detect missing dependency files
    gaps['missing_files'].extend(detect_missing_dependency_files(repository_analysis, file_names))
    
    # Detect missing configuration files
    gaps['missing_files'].extend(detect_missing_config_files(repository_analysis, file_names))
    
    # Detect missing Docker files
    gaps['missing_files'].extend(detect_missing_docker_files(file_names))
    
    # Detect configuration issues
    gaps['configuration_issues'].extend(detect_configuration_issues(repository_analysis))
    
    # Detect code issues
    gaps['code_issues'].extend(detect_code_issues(repository_analysis))
    
    # Detect security issues
    gaps['security_issues'].extend(detect_security_issues(repository_analysis, file_names))
    
    # Detect containerization gaps
    gaps['containerization_gaps'].extend(detect_containerization_gaps(repository_analysis))
    
    # Determine critical gaps
    gaps['critical_gaps'] = identify_critical_gaps(gaps)
    
    # Determine overall severity
    gaps['severity'] = determine_severity(gaps)
    
    return gaps


def detect_missing_dependency_files(analysis: Dict[str, Any], file_names: Set[str]) -> List[Dict[str, str]]:
    """Detect missing dependency management files."""
    missing = []
    language = analysis.get('language', '').lower()
    
    dependency_files = {
        'python': ['requirements.txt', 'pipfile', 'pyproject.toml', 'setup.py'],
        'javascript': ['package.json'],
        'typescript': ['package.json'],
        'go': ['go.mod'],
        'rust': ['cargo.toml'],
        'java': ['pom.xml', 'build.gradle'],
        'php': ['composer.json'],
        'ruby': ['gemfile'],
        'csharp': ['*.csproj', 'packages.config']
    }
    
    if language in dependency_files:
        required_files = dependency_files[language]
        found = any(req_file in file_names for req_file in required_files)
        
        if not found:
            missing.append({
                'type': 'dependency_file',
                'description': f'Missing dependency file for {language}',
                'suggested_files': required_files,
                'priority': 'high',
                'auto_generate': True
            })
    
    return missing


def detect_missing_config_files(analysis: Dict[str, Any], file_names: Set[str]) -> List[Dict[str, str]]:
    """Detect missing configuration files."""
    missing = []
    
    # Check for environment configuration
    env_files = ['.env', '.env.example', '.env.template', 'config.env']
    has_env_file = any(env_file in file_names for env_file in env_files)
    
    if analysis.get('environment_variables') and not has_env_file:
        missing.append({
            'type': 'environment_config',
            'description': 'Environment variables found but no .env file',
            'suggested_files': ['.env.example'],
            'priority': 'medium',
            'auto_generate': True
        })
    
    # Check for logging configuration
    framework = analysis.get('framework', '').lower()
    if framework in ['django', 'flask', 'express', 'spring']:
        logging_files = ['logging.conf', 'log4j.properties', 'logback.xml']
        has_logging = any(log_file in file_names for log_file in logging_files)
        
        if not has_logging:
            missing.append({
                'type': 'logging_config',
                'description': f'Missing logging configuration for {framework}',
                'suggested_files': ['logging.conf'],
                'priority': 'low',
                'auto_generate': True
            })
    
    return missing


def detect_missing_docker_files(file_names: Set[str]) -> List[Dict[str, str]]:
    """Detect missing Docker-related files."""
    missing = []
    
    # Check for Dockerfile
    if 'dockerfile' not in file_names:
        missing.append({
            'type': 'dockerfile',
            'description': 'Missing Dockerfile for containerization',
            'suggested_files': ['Dockerfile'],
            'priority': 'high',
            'auto_generate': True
        })
    
    # Check for .dockerignore
    if '.dockerignore' not in file_names:
        missing.append({
            'type': 'dockerignore',
            'description': 'Missing .dockerignore file',
            'suggested_files': ['.dockerignore'],
            'priority': 'medium',
            'auto_generate': True
        })

    # Check for .gitignore
    if '.gitignore' not in file_names:
        missing.append({
            'type': 'gitignore',
            'description': 'Missing .gitignore file',
            'suggested_files': ['.gitignore'],
            'priority': 'medium',
            'auto_generate': True
        })
    
    # Check for docker-compose
    compose_files = ['docker-compose.yml', 'docker-compose.yaml']
    has_compose = any(compose_file in file_names for compose_file in compose_files)
    
    if not has_compose:
        missing.append({
            'type': 'docker_compose',
            'description': 'Missing docker-compose.yml for multi-service setup',
            'suggested_files': ['docker-compose.yml'],
            'priority': 'medium',
            'auto_generate': True
        })
    
    return missing


def detect_configuration_issues(analysis: Dict[str, Any]) -> List[Dict[str, str]]:
    """Detect configuration-related issues."""
    issues = []
    
    # Check for hardcoded ports
    detected_ports = analysis.get('detected_ports', [])
    if detected_ports:
        # Check if ports are configurable
        env_vars = analysis.get('environment_variables', {})
        port_env_vars = [var for var in env_vars if 'port' in var.lower()]
        
        if not port_env_vars:
            issues.append({
                'type': 'hardcoded_ports',
                'description': f'Hardcoded ports detected: {detected_ports}',
                'suggestion': 'Make ports configurable via environment variables',
                'priority': 'medium',
                'auto_fix': True
            })
    
    # Check for missing environment variable defaults
    env_vars = analysis.get('environment_variables', {})
    if env_vars and not isinstance(env_vars, dict):
        issues.append({
            'type': 'env_var_defaults',
            'description': 'Environment variables without default values',
            'suggestion': 'Add default values for environment variables',
            'priority': 'low',
            'auto_fix': True
        })
    
    return issues


def detect_code_issues(analysis: Dict[str, Any]) -> List[Dict[str, str]]:
    """Detect code-related issues that affect containerization."""
    issues = []
    
    # Check for absolute paths
    if 'absolute_paths' in analysis:
        issues.append({
            'type': 'absolute_paths',
            'description': 'Absolute file paths detected',
            'suggestion': 'Convert to relative paths or use environment variables',
            'priority': 'high',
            'auto_fix': True
        })
    
    # Check for missing health check endpoints
    framework = analysis.get('framework', '').lower()
    if framework in ['flask', 'express', 'django', 'fastapi', 'spring']:
        # This would need to be detected during code analysis
        # For now, we'll suggest adding health checks
        issues.append({
            'type': 'health_check',
            'description': 'Missing health check endpoint',
            'suggestion': 'Add /health endpoint for container orchestration',
            'priority': 'medium',
            'auto_fix': True
        })
    
    return issues


def detect_security_issues(analysis: Dict[str, Any], file_names: Set[str]) -> List[Dict[str, str]]:
    """Detect security-related issues."""
    issues = []
    
    # Check for exposed secrets
    if 'secrets_exposed' in analysis:
        issues.append({
            'type': 'exposed_secrets',
            'description': 'Potential secrets found in code',
            'suggestion': 'Move secrets to environment variables',
            'priority': 'critical',
            'auto_fix': False
        })
    
    # Check for missing .gitignore
    if '.gitignore' not in file_names:
        issues.append({
            'type': 'missing_gitignore',
            'description': 'Missing .gitignore file',
            'suggestion': 'Add .gitignore to prevent committing sensitive files',
            'priority': 'medium',
            'auto_fix': True
        })
    
    # Check for insecure dependencies
    dependencies = analysis.get('dependencies', [])
    if dependencies:
        # This would require a vulnerability database
        # For now, we'll suggest dependency scanning
        issues.append({
            'type': 'dependency_security',
            'description': 'Dependencies should be scanned for vulnerabilities',
            'suggestion': 'Run security audit on dependencies',
            'priority': 'medium',
            'auto_fix': False
        })
    
    return issues


def detect_containerization_gaps(analysis: Dict[str, Any]) -> List[Dict[str, str]]:
    """Detect gaps specific to containerization."""
    gaps = []
    
    # Check for missing entry point
    entry_points = analysis.get('entry_points', [])
    if not entry_points:
        gaps.append({
            'type': 'entry_point',
            'description': 'No clear application entry point identified',
            'suggestion': 'Identify main application file or startup script',
            'priority': 'high',
            'auto_fix': False
        })
    
    # Check for database dependencies without setup
    db_connections = analysis.get('database_requirements', [])
    if db_connections:
        gaps.append({
            'type': 'database_setup',
            'description': 'Database dependencies detected',
            'suggestion': 'Add database service to docker-compose.yml',
            'priority': 'high',
            'auto_fix': True
        })
    
    # Check for external service dependencies
    external_services = analysis.get('external_services', [])
    if external_services:
        gaps.append({
            'type': 'external_services',
            'description': f'External services detected: {external_services}',
            'suggestion': 'Document external service requirements',
            'priority': 'medium',
            'auto_fix': False
        })
    
    return gaps


def identify_critical_gaps(gaps: Dict[str, List]) -> List[str]:
    """Identify gaps that are critical for containerization."""
    critical = []
    
    # Critical missing files
    for gap in gaps.get('missing_files', []):
        if gap.get('priority') == 'high':
            critical.append(gap['type'])
    
    # Critical security issues
    for issue in gaps.get('security_issues', []):
        if issue.get('priority') == 'critical':
            critical.append(issue['type'])
    
    # Critical code issues
    for issue in gaps.get('code_issues', []):
        if issue.get('priority') == 'high':
            critical.append(issue['type'])
    
    return critical


def determine_severity(gaps: Dict[str, List]) -> str:
    """Determine overall severity of gaps."""
    critical_count = len(gaps.get('critical_gaps', []))
    high_priority_count = sum(
        1 for gap_list in gaps.values() 
        if isinstance(gap_list, list)
        for gap in gap_list 
        if isinstance(gap, dict) and gap.get('priority') == 'high'
    )
    
    if critical_count > 0:
        return 'critical'
    elif high_priority_count > 3:
        return 'high'
    elif high_priority_count > 0:
        return 'medium'
    else:
        return 'low'


if __name__ == "__main__":
    # Test the gap detector
    test_analysis = {
        'language': 'python',
        'framework': 'flask',
        'detected_ports': [5000],
        'environment_variables': ['DATABASE_URL', 'SECRET_KEY'],
        'database_requirements': ['postgresql'],
        'dependencies': ['flask', 'psycopg2']
    }
    
    test_file_tree = [
        {'type': 'file', 'name': 'app.py', 'path': 'app.py'},
        {'type': 'file', 'name': 'README.md', 'path': 'README.md'},
        {'type': 'directory', 'name': 'static', 'path': 'static'}
    ]
    
    gaps = detect_gaps(test_analysis, test_file_tree)
    
    print("Detected gaps:")
    for gap_type, gap_list in gaps.items():
        if gap_list:
            print(f"\n{gap_type}:")
            for gap in gap_list:
                print(f"  - {gap}")
